package model

// RemoveDuplicates removes duplicate FileData entries based on DownloadUrl and FileName
func RemoveDuplicates(fileDetails []FileData) []FileData {
	uniqueMap := make(map[string]bool)
	var result []FileData
	for _, file := range fileDetails {
		key := file.DownloadUrl + "::" + file.FileName // Unique key based on DownloadUrl and FileName
		if !uniqueMap[key] {
			uniqueMap[key] = true
			result = append(result, file)
		}
	}
	return result
}
